"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check, Sparkles } from "lucide-react";
import { subscriptionService } from "@/services/api/subscriptionService";
import { FEATURES } from "@/config/features";
import { useTranslations } from "next-intl";
import { safeOpenUrl } from "@/lib/browserUtils";
import { trackEvent } from "@/lib/analytics";

export function DashboardPromotionCard() {
  const t = useTranslations("dashboard.promotionCard");
  const [isLoading, setIsLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState({
    minutes: 5,
    seconds: 0,
    milliseconds: 0,
  });
  const [isPromotionActive, setIsPromotionActive] = useState(true);
  const { plan, discountPercentage, promoCode } = FEATURES.PROMO_CARD || {};

  const PROMO_DURATION = 10 * 60 * 1000; // 10分钟的毫秒数

  // 价格计算逻辑
  const getOriginalPrice = (plan) => {
    const prices = {
      basic_yearly: 120,
      basic_monthly: 10,
      pro_yearly: 360,
      pro_monthly: 30,
    };
    return prices[plan] || 120;
  };

  const calculateDiscountedPrice = (originalPrice, discountPercentage) => {
    if (!isPromotionActive) return originalPrice;
    return originalPrice * (1 - discountPercentage / 100);
  };

  const originalPrice = getOriginalPrice(plan);
  const discountedPrice = calculateDiscountedPrice(
    originalPrice,
    discountPercentage
  );
  const isYearlyPlan = plan?.includes("year");

  // 修改检查促销状态逻辑
  const checkPromoStatus = () => {
    const promoStatus = localStorage.getItem("promoStatus");
    const now = Date.now();

    if (!promoStatus) {
      // 首次访问
      const endTime = now + PROMO_DURATION;
      localStorage.setItem(
        "promoStatus",
        JSON.stringify({
          endTime,
          lastPromoTime: now,
        })
      );
      return endTime;
    }

    const { endTime } = JSON.parse(promoStatus);

    // 如果当前时间已经超过了结束时间，计算应该在哪个周期
    if (now > endTime) {
      const elapsedTime = now - endTime;
      const completedCycles = Math.floor(elapsedTime / PROMO_DURATION);
      const newEndTime = endTime + (completedCycles + 1) * PROMO_DURATION;

      localStorage.setItem(
        "promoStatus",
        JSON.stringify({
          endTime: newEndTime,
          lastPromoTime: now,
        })
      );
      return newEndTime;
    }

    return endTime;
  };

  useEffect(() => {
    const endTime = checkPromoStatus();

    const timer = setInterval(() => {
      const remaining = endTime - Date.now();

      if (remaining <= 0) {
        // 当倒计时结束时，重新检查状态并开始新的促销
        const newEndTime = checkPromoStatus();
        const newRemaining = newEndTime - Date.now();

        const minutes = Math.floor(newRemaining / (60 * 1000));
        const seconds = Math.floor((newRemaining % (60 * 1000)) / 1000);
        const milliseconds = Math.floor((newRemaining % 1000) / 10);

        setTimeLeft({ minutes, seconds, milliseconds });
        setIsPromotionActive(true); // 保持促销活动状态
        return;
      }

      const minutes = Math.floor(remaining / (60 * 1000));
      const seconds = Math.floor((remaining % (60 * 1000)) / 1000);
      const milliseconds = Math.floor((remaining % 1000) / 10);

      setTimeLeft({ minutes, seconds, milliseconds });
      setIsPromotionActive(true);
    }, 10);

    return () => clearInterval(timer);
  }, []);

  const handleUpgradeClick = async () => {
    setIsLoading(true);
    try {
      const response = await subscriptionService.createCheckoutSession(
        plan,
        promoCode,
        "subscription",
        "promotion_card"
      );

      // 添加 umami 收入追踪
      if (response.status === 200) {
        // 构造具体的事件名称用于收入报告分组
        const planType = isYearlyPlan ? "yearly" : "monthly";
        const tier = plan?.includes("pro") ? "pro" : "basic";
        const eventName = `revenue_${tier}_${planType}_promo`;

        trackEvent(eventName, {
          revenue: discountedPrice,
          currency: "USD",
        });
      }

      const url = response.data.url;
      if (url) {
        safeOpenUrl(url);
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    t("features.minutes"),
    t("features.model"),
    // t("features.export"),
    t("features.speakerIdentification"),
    t("features.support"),
  ];

  return (
    <Card className="w-full overflow-hidden">
      {/* Header */}
      <div className="relative bg-gradient-to-r from-custom-bg-500 to-custom-bg-600 p-4 text-white">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h2 className="text-xl font-bold mb-1">
              {isPromotionActive ? t("limitedTimeOffer") : t("regularPrice")}
            </h2>
            <p className="text-custom-bg-100 text-sm font-medium">
              {isPromotionActive
                ? t("discountedYearlyPlan")
                : t("yearlyBasicPlan")}
            </p>
          </div>
          <div className="flex items-center gap-4">
            {/* Urgency indicators - 倒计时 */}
            {isPromotionActive && (
              <div className="flex items-center gap-1 font-mono">
                {/* Minutes */}
                <div className="bg-white/20 rounded px-2 py-1 text-white text-sm">
                  {String(timeLeft.minutes).padStart(2, "0")}
                </div>
                <span className="text-white/70 font-bold">:</span>
                {/* Seconds */}
                <div className="bg-white/20 rounded px-2 py-1 text-white text-sm">
                  {String(timeLeft.seconds).padStart(2, "0")}
                </div>
                <span className="text-white/70 font-bold">:</span>
                {/* Milliseconds */}
                <div className="bg-white/20 rounded px-2 py-1 text-white text-sm">
                  {String(timeLeft.milliseconds).padStart(2, "0")}
                </div>
              </div>
            )}
            <Sparkles className="w-5 h-5 animate-pulse text-yellow-300" />
          </div>
        </div>
      </div>

      {/* Content - Features and Pricing in responsive layout */}
      <div className="p-4 flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 lg:gap-8">
        {/* Features */}
        <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-2">
          {features.map((feature, i) => (
            <div key={i} className="flex items-center gap-2">
              <div className="h-4 w-4 rounded-full bg-custom-bg-100 flex items-center justify-center flex-shrink-0">
                <Check className="h-2.5 w-2.5 text-custom-bg-600" />
              </div>
              <span className="text-xs text-gray-600 font-medium">
                {feature}
              </span>
            </div>
          ))}
        </div>

        {/* Pricing and Action */}
        <div className="flex flex-col sm:flex-row items-center gap-4 lg:gap-6">
          {/* Price Display */}
          <div className="text-center lg:text-right">
            <div className="flex items-center justify-center lg:justify-end gap-2 mb-1">
              {isPromotionActive && (
                <span className="text-gray-400 line-through text-sm font-medium">
                  ${originalPrice.toFixed(2)}
                </span>
              )}
              <span className="text-2xl font-bold text-custom-bg-600">
                ${discountedPrice.toFixed(2)}
              </span>
              <span className="text-gray-500 text-sm font-medium">
                {isYearlyPlan ? t("perYear") : t("perMonth")}
              </span>
            </div>
            {isYearlyPlan && (
              <p className="text-xs text-gray-500 font-medium">
                {t("justPerMonth", {
                  price: (discountedPrice / 12).toFixed(2),
                })}
              </p>
            )}
          </div>

          {/* Action Button */}
          <Button
            className="bg-custom-bg hover:bg-custom-bg-600 transition-all duration-300 h-10 px-6 text-sm font-medium whitespace-nowrap w-full sm:w-auto"
            onClick={handleUpgradeClick}
            disabled={isLoading}
          >
            {isLoading ? t("processing") : t("upgradeNow")}
          </Button>
        </div>
      </div>
    </Card>
  );
}
